import axios, { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios'

const API_URL = import.meta.env.VITE_API_URL || ''  // Empty string to use relative URLs with Vite proxy
const MAX_RETRIES = 3
const RETRY_DELAY = 1000 // 1 second base delay

// Extend axios config type to include retryCount
interface ExtendedAxiosConfig extends InternalAxiosRequestConfig {
  retryCount?: number
}

class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_URL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 seconds timeout
    })

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('access_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor with retry logic
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const config = error.config as ExtendedAxiosConfig
        if (!config) return Promise.reject(error)

        config.retryCount = config.retryCount || 0

        const shouldRetry = (
          config.retryCount < MAX_RETRIES &&
          (error.code === 'ERR_NETWORK' ||
            error.code === 'ERR_INSUFFICIENT_RESOURCES' ||
            (error.response?.status ?? 0) === 429 || // Too Many Requests
            (error.response?.status ?? 0) >= 500)    // Server errors
        )

        if (shouldRetry) {
          config.retryCount += 1
          // Exponential backoff with jitter
          const delay = RETRY_DELAY * Math.pow(2, config.retryCount - 1) * (0.5 + Math.random())
          await new Promise(resolve => setTimeout(resolve, delay))
          return this.client(config)
        }

        return Promise.reject(error)
      }
    )
  }

  // Helper methods with proper typing
  async get<T>(url: string): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url)
  }

  async post<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data)
  }

  async put<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data)
  }

  async delete<T>(url: string): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url)
  }
}

export const api = new ApiClient()
export default api
