import React, { useState } from 'react'
import { Card, Row, Col, Button, Modal, Form, Input, Typography, Space } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Error<PERSON><PERSON>t,
  showDeleteConfirm,
  showUpdateConfirm
} from '../components'
import { useApi, useMutation } from '../hooks'
import { categoriesService } from '../client/services'
import type { Category, CategoryCreate, CategoryUpdate } from '../client/types'
import { formatDate, formatId, commonRules, truncateText } from '../utils'

const { Title, Text } = Typography

const Categories: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(9)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [form] = Form.useForm()
  // Fetch categories
  const {
    data: categoriesResponse,
    loading: categoriesLoading,
    error: categoriesError,
    execute: refetchCategories
  } = useApi(
    React.useCallback(() => categoriesService.getAll(), []),
    { immediate: true }
  )

  // Mutations
  const createMutation = useMutation(
    React.useCallback((data: CategoryCreate) => categoriesService.create(data), []),
    {
      onSuccess: React.useCallback(() => {
        refetchCategories();
        setIsModalVisible(false);
        form.resetFields();
      }, [refetchCategories, form])
    }
  )

  const updateMutation = useMutation(
    React.useCallback(
      ({ id, data }: { id: string; data: CategoryUpdate }) => categoriesService.update(id, data),
      []
    ),
    {
      onSuccess: React.useCallback(() => {
        refetchCategories();
        setIsModalVisible(false);
        form.resetFields();
      }, [refetchCategories, form])
    }
  )

  const deleteMutation = useMutation(
    React.useCallback((id: string) => categoriesService.delete(id), []),
    {
      onSuccess: React.useCallback(() => refetchCategories(), [refetchCategories])
    }
  )

  const categories = categoriesResponse?.data || []
  const totalCategories = categoriesResponse?.count || 0

  // Handlers
  const showAddModal = () => {
    setModalMode('add')
    setEditingCategory(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const showEditModal = (category: Category) => {
    setModalMode('edit')
    setEditingCategory(category)
    form.setFieldsValue({
      name: category.name,
      description: category.description,
    })
    setIsModalVisible(true)
  }

  const handleDelete = (category: Category) => {
    showDeleteConfirm(category.name, () => deleteMutation.mutate(category.id))
  }

  const handleCancel = () => {
    form.resetFields()
    setIsModalVisible(false)
  }

  const onFinish = async (values: CategoryCreate | CategoryUpdate) => {
    if (modalMode === 'add') {
      createMutation.mutate(values as CategoryCreate)
    } else if (modalMode === 'edit' && editingCategory) {
      showUpdateConfirm(values.name || editingCategory.name, () => {
        updateMutation.mutate({ id: editingCategory.id, data: values })
      })
    }
  }

  if (categoriesLoading) return <LoadingSpinner tip="Loading categories..." />
  if (categoriesError) return <ErrorAlert description={categoriesError} onRetry={refetchCategories} />

  return (
    <>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Categories</Title>
        <Text type="secondary">Manage your product categories</Text>
      </div>

      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
          loading={createMutation.loading}
        >
          Add Category
        </Button>
      </div>

      <Row gutter={[16, 16]}>
        {categories.map((category) => (
          <Col xs={24} sm={12} md={8} key={category.id}>
            <Card
              hoverable
              actions={[
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  onClick={() => showEditModal(category)}
                  key="edit"
                >
                  Edit
                </Button>,
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(category)}
                  key="delete"
                >
                  Delete
                </Button>,
              ]}
            >
              <Card.Meta
                title={category.name}
                description={
                  <div>
                    <div style={{ marginBottom: 8 }}>
                      {truncateText(category.description || 'No description', 80)}
                    </div>
                    <div style={{ fontSize: 12, color: '#999' }}>
                      ID: {formatId(category.id)}
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {categories.length === 0 && (
        <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
          No categories found. Click "Add Category" to create your first category.
        </div>
      )}

      <Modal
        title={modalMode === 'add' ? 'Add Category' : 'Edit Category'}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={() => form.submit()}
        okText={modalMode === 'add' ? 'Add' : 'Update'}
        confirmLoading={createMutation.loading || updateMutation.loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ name: '', description: '' }}
        >
          <Form.Item
            name="name"
            label="Category Name"
            rules={commonRules.name}
          >
            <Input placeholder="Enter category name" />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={commonRules.description}
          >
            <Input.TextArea
              rows={3}
              placeholder="Enter category description"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default Categories