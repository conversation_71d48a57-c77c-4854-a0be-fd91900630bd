import api from './api'

const apiClient = api
import type {
  Product,
  ProductCreate,
  ProductUpdate,
  ProductsParams,
  Category,
  CategoryCreate,
  CategoryUpdate,
  Store,
  StoreCreate,
  StoreUpdate,
  Customer,
  CustomerCreate,
  CustomerUpdate,
  Order,
  OrderCreate,
  OrderUpdate,
  Variant,
  VariantCreate,
  VariantUpdate,
  VariantsParams,
  OrderDetail,
  OrderDetailCreate,
  OrderDetailUpdate,
  OrderDetailsParams,
  ApiResponse,
  PaginatedResponse,
} from './types'

// Products Service
export const productsService = {
  getAll: (params?: ProductsParams) =>
    apiClient.get<PaginatedResponse<Product>>('/api/v1/products', { params }),

  getById: (id: string) =>
    apiClient.get<Product>(`/api/v1/products/${id}`),

  create: (data: ProductCreate) =>
    apiClient.post<Product>('/api/v1/products', data),

  update: (id: string, data: ProductUpdate) =>
    apiClient.put<Product>(`/api/v1/products/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/products/${id}`),
}

// Categories Service
export const categoriesService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Category>>('/api/v1/categories'),

  getById: (id: string) =>
    apiClient.get<Category>(`/api/v1/categories/${id}`),

  create: (data: CategoryCreate) =>
    apiClient.post<Category>('/api/v1/categories', data),

  update: (id: string, data: CategoryUpdate) =>
    apiClient.put<Category>(`/api/v1/categories/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/categories/${id}`),
}

// Stores Service
export const storesService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Store>>('/api/v1/stores'),

  getById: (id: string) =>
    apiClient.get<Store>(`/api/v1/stores/${id}`),

  create: (data: StoreCreate) =>
    apiClient.post<Store>('/api/v1/stores', data),

  update: (id: string, data: StoreUpdate) =>
    apiClient.put<Store>(`/api/v1/stores/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/stores/${id}`),
}

// Customers Service
export const customersService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Customer>>('/api/v1/customers'),

  getById: (id: string) =>
    apiClient.get<Customer>(`/api/v1/customers/${id}`),

  create: (data: CustomerCreate) =>
    apiClient.post<Customer>('/api/v1/customers', data),

  update: (id: string, data: CustomerUpdate) =>
    apiClient.put<Customer>(`/api/v1/customers/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/customers/${id}`),
}

// Orders Service
export const ordersService = {
  getAll: () =>
    apiClient.get<PaginatedResponse<Order>>('/api/v1/orders'),

  getById: (id: string) =>
    apiClient.get<Order>(`/api/v1/orders/${id}`),

  create: (data: OrderCreate) =>
    apiClient.post<Order>('/api/v1/orders', data),

  update: (id: string, data: OrderUpdate) =>
    apiClient.put<Order>(`/api/v1/orders/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/orders/${id}`),
}

// Variants Service
export const variantsService = {
  getAll: (params?: VariantsParams) =>
    apiClient.get<PaginatedResponse<Variant>>('/api/v1/variants', { params }),

  getById: (id: string) =>
    apiClient.get<Variant>(`/api/v1/variants/${id}`),

  getByProduct: (productId: string) =>
    apiClient.get<{ data: Variant[], count: number }>('/variants', { product_id: productId }),

  create: (data: VariantCreate) =>
    apiClient.post<Variant>('/api/v1/variants', data),

  update: (id: string, data: VariantUpdate) =>
    apiClient.put<Variant>(`/api/v1/variants/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/variants/${id}`),
}

// Order Details Service
export const orderDetailsService = {
  getAll: (params?: OrderDetailsParams) =>
    apiClient.get<PaginatedResponse<OrderDetail>>('/api/v1/order_details', { params }),

  getById: (id: string) =>
    apiClient.get<OrderDetail>(`/api/v1/order_details/${id}`),

  getByOrder: (orderId: string) =>
    apiClient.get<PaginatedResponse<OrderDetail>>('/api/v1/order_details', { params: { order_id: orderId } }),

  create: (data: OrderDetailCreate) =>
    apiClient.post<OrderDetail>('/api/v1/order_details', data),

  update: (id: string, data: OrderDetailUpdate) =>
    apiClient.put<OrderDetail>(`/api/v1/order_details/${id}`, data),

  delete: (id: string) =>
    apiClient.delete(`/api/v1/order_details/${id}`),
}

// Export all services
export const services = {
  products: productsService,
  categories: categoriesService,
  stores: storesService,
  customers: customersService,
  orders: ordersService,
  variants: variantsService,
  orderDetails: orderDetailsService,
}

export default services
