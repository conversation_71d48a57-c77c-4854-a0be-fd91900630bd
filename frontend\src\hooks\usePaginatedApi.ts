import { useState, useCallback, useEffect } from 'react'
import { AxiosResponse } from 'axios'
import { useApi } from './useApi'

interface PaginatedResponse<T> {
  data: T[]
  count: number
}

export function usePaginatedApi<T>(
  apiCall: (page: number, pageSize: number) => Promise<AxiosResponse<PaginatedResponse<T>>>,
  initialPage = 1,
  initialPageSize = 10
) {
  const [page, setPage] = useState(initialPage)
  const [pageSize, setPageSize] = useState(initialPageSize)

  // Debounced state for page and pageSize
  const [debouncedPage, setDebouncedPage] = useState(page)
  const [debouncedPageSize, setDebouncedPageSize] = useState(pageSize)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedPage(page)
      setDebouncedPageSize(pageSize)
    }, 300) // debounce delay 300ms

    return () => {
      clearTimeout(handler)
    }
  }, [page, pageSize])

  const fetchData = useCallback(
    () => apiCall(debouncedPage, debouncedPageSize),
    [apiCall, debouncedPage, debouncedPageSize]
  )

  const {
    data: response,
    loading,
    error,
    execute,
  } = useApi<PaginatedResponse<T>>(fetchData, {
    deps: [debouncedPage, debouncedPageSize], // Only re-fetch when debounced values change
  })

  const changePage = useCallback((newPage: number, newPageSize?: number) => {
    setPage(newPage)
    if (newPageSize !== undefined) {
      setPageSize(newPageSize)
    }
  }, [])

  return {
    data: response?.data || [],
    total: response?.count || 0,
    page,
    pageSize,
    loading,
    error,
    changePage,
  }
}
