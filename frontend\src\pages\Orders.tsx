import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, Modal, Form, Select, InputNumber, DatePicker, Typography, Space, Tag } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'
import { 
  LoadingSpinner, 
  ErrorAlert, 
  DataTable,
  showDeleteConfirm, 
  showUpdateConfirm 
} from '../components'
import { useApi, useMutation } from '../hooks'
import { ordersService, customersService, storesService } from '../client/services'
import type { Order, OrderCreate, OrderUpdate, TableColumn } from '../client/types'
import { formatDate, formatId, formatCurrency, commonRules } from '../utils'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select

const Orders: React.FC = () => {
  const navigate = useNavigate()
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add')
  const [editingOrder, setEditingOrder] = useState<Order | null>(null)
  const [form] = Form.useForm()

  // Fetch orders
  const { 
    data: ordersResponse, 
    loading: ordersLoading, 
    error: ordersError, 
    execute: refetchOrders 
  } = useApi(() => ordersService.getAll())

  // Fetch customers and stores for dropdowns
  const { data: customersResponse } = useApi(() => customersService.getAll())
  const { data: storesResponse } = useApi(() => storesService.getAll())

  // Mutations
  const createMutation = useMutation(
    (data: OrderCreate) => ordersService.create(data),
    { onSuccess: () => { refetchOrders(); setIsModalVisible(false); form.resetFields() } }
  )

  const updateMutation = useMutation(
    ({ id, data }: { id: string; data: OrderUpdate }) => ordersService.update(id, data),
    { onSuccess: () => { refetchOrders(); setIsModalVisible(false); form.resetFields() } }
  )

  const deleteMutation = useMutation(
    (id: string) => ordersService.delete(id),
    { onSuccess: () => refetchOrders() }
  )

  const orders = ordersResponse?.data || []
  const customers = customersResponse?.data || []
  const stores = storesResponse?.data || []

  // Helper functions
  const getCustomerName = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    return customer?.name || customer?.username || 'Unknown Customer'
  }

  const getStoreName = (storeId: string) => {
    const store = stores.find(s => s.id === storeId)
    return store?.name || store?.name_store || 'Unknown Store'
  }

  // Generate sequential order number
  const getOrderNumber = (index: number) => {
    return `#${String(index + 1).padStart(4, '0')}`
  }

  // Handlers
  const showAddModal = () => {
    setModalMode('add')
    setEditingOrder(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  const showEditModal = (order: Order) => {
    setModalMode('edit')
    setEditingOrder(order)
    form.setFieldsValue({
      customer_id: order.customer_id,
      store_id: order.store_id,
      total_amount: order.total_amount,
      order_date: order.order_date ? dayjs(order.order_date) : null,
    })
    setIsModalVisible(true)
  }

  const handleDelete = (order: Order, index: number) => {
    const orderNumber = getOrderNumber(index)
    showDeleteConfirm(`Order ${orderNumber}`, () => deleteMutation.mutate(order.id))
  }

  const handleCancel = () => {
    form.resetFields()
    setIsModalVisible(false)
  }

  const onFinish = async (values: any) => {
    const orderData = {
      ...values,
      order_date: values.order_date ? values.order_date.toISOString() : undefined,
    }

    if (modalMode === 'add') {
      createMutation.mutate(orderData as OrderCreate)
    } else if (modalMode === 'edit' && editingOrder) {
      const orderIndex = orders.findIndex(o => o.id === editingOrder.id)
      const orderNumber = getOrderNumber(orderIndex)
      showUpdateConfirm(`Order ${orderNumber}`, () => {
        updateMutation.mutate({ id: editingOrder.id, data: orderData })
      })
    }
  }

  const handleOrderClick = (orderId: string) => {
    navigate(`/orders/${orderId}`)
  }

  // Table columns
  const columns: TableColumn<Order>[] = [
    {
      key: 'order_number',
      title: 'Order #',
      render: (_, record: Order, index: number) => (
        <Button 
          type="link" 
          onClick={() => handleOrderClick(record.id)}
          style={{ padding: 0, fontWeight: 'bold' }}
        >
          {getOrderNumber(index)}
        </Button>
      ),
      width: 100,
    },
    {
      key: 'customer',
      title: 'Customer',
      render: (_, record: Order) => getCustomerName(record.customer_id),
      sorter: true,
    },
    {
      key: 'store',
      title: 'Store',
      render: (_, record: Order) => getStoreName(record.store_id),
    },
    {
      key: 'total_amount',
      title: 'Total Amount',
      dataIndex: 'total_amount',
      render: (amount: number) => amount ? formatCurrency(amount) : 'Not set',
      sorter: true,
    },
    {
      key: 'order_date',
      title: 'Order Date',
      dataIndex: 'order_date',
      render: (date: string) => date ? formatDate(date) : 'Not set',
      sorter: true,
    },
    {
      key: 'status',
      title: 'Status',
      render: () => <Tag color="processing">Processing</Tag>, // Default status
    },
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      render: (id: string) => formatId(id),
      width: 100,
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record: Order, index: number) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleOrderClick(record.id)}
            size="small"
          >
            View
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            size="small"
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record, index)}
            size="small"
          >
            Delete
          </Button>
        </Space>
      ),
      width: 150,
    },
  ]

  if (ordersLoading) return <LoadingSpinner tip="Loading orders..." />
  if (ordersError) return <ErrorAlert description={ordersError} onRetry={refetchOrders} />

  return (
    <>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Orders</Title>
        <Text type="secondary">Manage customer orders and transactions</Text>
      </div>

      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showAddModal}
          loading={createMutation.loading}
        >
          Add Order
        </Button>
      </div>

      <DataTable
        data={orders}
        columns={columns}
        loading={ordersLoading || deleteMutation.loading}
        emptyText="No orders found. Click 'Add Order' to create your first order."
        onRow={(record) => ({
          onDoubleClick: () => handleOrderClick(record.id),
        })}
      />

      <Modal
        title={modalMode === 'add' ? 'Add Order' : 'Edit Order'}
        open={isModalVisible}
        onCancel={handleCancel}
        onOk={() => form.submit()}
        okText={modalMode === 'add' ? 'Add' : 'Update'}
        confirmLoading={createMutation.loading || updateMutation.loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{ customer_id: '', store_id: '', total_amount: undefined, order_date: null }}
        >
          <Form.Item
            name="customer_id"
            label="Customer"
            rules={[{ required: true, message: 'Please select a customer!' }]}
          >
            <Select placeholder="Select a customer" showSearch optionFilterProp="children">
              {customers.map((customer) => (
                <Option key={customer.id} value={customer.id}>
                  {customer.name || customer.username} ({customer.username})
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="store_id"
            label="Store"
            rules={[{ required: true, message: 'Please select a store!' }]}
          >
            <Select placeholder="Select a store" showSearch optionFilterProp="children">
              {stores.map((store) => (
                <Option key={store.id} value={store.id}>
                  {store.name || store.name_store}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="total_amount"
            label="Total Amount"
            rules={commonRules.price}
          >
            <InputNumber 
              placeholder="Enter total amount" 
              style={{ width: '100%' }}
              min={0}
              step={0.01}
              precision={2}
              formatter={value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value!.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name="order_date"
            label="Order Date"
          >
            <DatePicker 
              style={{ width: '100%' }}
              showTime
              placeholder="Select order date and time"
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default Orders
